# Product Overview

This is a FastMCP boilerplate project - a starting template for building Model Context Protocol (MCP) servers using the FastMCP framework.

## Purpose
- Provides a foundation for creating MCP servers that can integrate with AI assistants
- Demonstrates best practices for MCP tool development with proper testing, linting, and CI/CD setup
- Serves as a reference implementation showing how to define tools, resources, and prompts

## Key Features
- Example MCP tool implementation (addition calculator)
- Resource serving capabilities (application logs)
- Prompt templates (git commit message generation)
- Complete development workflow with testing and publishing pipeline

## Target Use Case
Developers building MCP servers to extend AI assistant capabilities with custom tools and integrations.