# Technology Stack

## Core Technologies
- **Runtime**: Node.js 22+ (ES modules)
- **Language**: TypeScript 5.8+
- **Framework**: FastMCP 1.27+ for MCP server implementation
- **Validation**: Zod for schema validation and type safety

## Development Tools
- **Build**: TypeScript compiler (`tsc`)
- **Testing**: Vitest for unit testing
- **Linting**: ESLint with TypeScript support + Perfectionist plugin for import sorting
- **Formatting**: Prettier
- **Development Server**: tsx for TypeScript execution

## Common Commands

### Development
```bash
npm run dev          # Start development server with CLI interaction
npm run start        # Start production server
```

### Code Quality
```bash
npm run test         # Run unit tests
npm run lint         # Check linting, formatting, and type errors
npm run format       # Auto-format code and fix linting issues
npm run build        # Compile TypeScript to dist/
```

## Configuration Files
- `tsconfig.json`: Extends @tsconfig/node22, outputs to `dist/`
- `eslint.config.ts`: Flat config with recommended rules + alphabetical sorting
- `package.json`: ES modules, semantic-release for automated publishing

## Architecture Patterns
- MCP tools should be implemented as pure functions in separate modules
- Use Zod schemas for parameter validation
- Export business logic separately from MCP server configuration for testability
- Follow the pattern: implement → test → integrate with server