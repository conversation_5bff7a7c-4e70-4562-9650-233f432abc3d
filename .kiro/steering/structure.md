# Project Structure

## Root Level
- Configuration files for build tools, linting, and package management
- `README.md` with comprehensive setup and usage instructions
- `LICENSE` (MIT) and `.gitignore` for repository management

## Source Organization (`src/`)
```
src/
├── server.ts        # Main MCP server configuration and startup
├── [tool].ts        # Individual tool implementations (e.g., add.ts)
└── [tool].test.ts   # Unit tests for each tool
```

## Key Patterns

### Server Configuration (`server.ts`)
- Single entry point that configures and starts the FastMCP server
- Imports and registers all tools, resources, and prompts
- Uses `stdio` transport type for MCP communication

### Tool Implementation
- Each tool gets its own module (e.g., `add.ts`)
- Export pure functions that contain the business logic
- Keep MCP-specific configuration separate from implementation

### Testing Strategy
- Test the business logic functions, not the MCP server itself
- One test file per implementation file
- Use descriptive test names following "should [behavior]" pattern

## Build Output
- Compiled JavaScript goes to `dist/` directory
- Main entry point: `dist/index.js` (though server.ts is the actual entry)

## Development Workflow
1. Implement tool logic in separate module
2. Write unit tests for the logic
3. Register tool in `server.ts` with proper Zod schema
4. Test integration using `npm run dev`